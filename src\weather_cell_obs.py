#!/usr/bin/env python3
# coding: utf-8
"""
实况天气数据处理脚本
处理实况数据的下载、解析和入库，支持所有实况字段：
- PRE10m: 10分钟降水（单位：毫米）
- PRE1h: 最近一小时降水（单位：毫米）
- PRS: 气压（单位：百帕）
- PRSSea: 海洋气压（单位：百帕）
- RHU: 湿度（单位：百分比）
- TEM: 温度（单位：摄氏度）
- U: 气象风场U分量（单位：米/秒）
- V: 气象风场V分量（单位：米/秒）
- VIS: 能见度（单位：千米）
- WEATHER: 天气现象编码
- WIND: 风向编码
- WINS: 风力编码
"""

import os, math, uuid, multiprocessing as mp, asyncio
import numpy as np, pandas as pd, xarray as xr
from sqlalchemy import create_engine
import asyncpg
import logging
from typing import Dict, List
from concurrent.futures import ProcessPoolExecutor
import argparse
from datetime import datetime, timedelta

# 导入统一配置
from config import (
    PG_URL, GRID_X_MIN, GRID_Y_MIN, GRID_X_MAX, GRID_Y_MAX, GRID_SIZE,
    STEP, LON_OFF, LAT_OFF, PROCESSING_CONFIG, WEATHER_DOWNLOAD_CONFIG
)

# 导入天气数据下载模块
try:
    from weather_download import download_and_get_latest_weather_data, process_and_backup_weather_file
    WEATHER_DOWNLOAD_AVAILABLE = True
except ImportError:
    WEATHER_DOWNLOAD_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ─────────────── 全局配置 ───────────────
# 格网生成顺序：先Y后X，所以行数是Y方向，列数是X方向
NC_NROWS = int(round((GRID_Y_MAX-GRID_Y_MIN)/GRID_SIZE))   # 820 (Y方向)
NC_NCOLS = int(round((GRID_X_MAX-GRID_X_MIN)/GRID_SIZE))   # 875 (X方向)

PROCESSES = PROCESSING_CONFIG["max_processes"] or max(1, mp.cpu_count() - 1)  # 并行进程数拉满
CHUNK_SIZE = PROCESSING_CONFIG["chunk_size"]                                   # 一个进程一次处理多少 time index

# 协程配置 - 优化为高性能模式
MAX_CONCURRENT_DB_OPERATIONS = 20  # 增加并发数据库操作数
MAX_CONCURRENT_PROCEDURES = 10     # 提高存储过程并发数到10个

# 实况数据字段配置
OBS_FIELD_TYPES = {
    'PRE10m': {
        'var_name': 'PRE10m',
        'data_type': 'float',
        'scale_factor': 1.0,
        'description': '10分钟降水',
        'db_field': 'pre10m'
    },
    'PRE1h': {
        'var_name': 'PRE1h',
        'data_type': 'float',
        'scale_factor': 1.0,
        'description': '最近一小时降水',
        'db_field': 'pre1h'
    },
    'PRS': {
        'var_name': 'PRS',
        'data_type': 'float',
        'scale_factor': 1.0,
        'description': '气压',
        'db_field': 'prs'
    },
    'PRSSea': {
        'var_name': 'PRSSea',
        'data_type': 'float',
        'scale_factor': 1.0,
        'description': '海洋气压',
        'db_field': 'prs_sea'
    },
    'RHU': {
        'var_name': 'RHU',
        'data_type': 'float',
        'scale_factor': 1.0,
        'description': '湿度',
        'db_field': 'rhu'
    },
    'TEM': {
        'var_name': 'TEM',
        'data_type': 'float',
        'scale_factor': 1.0,
        'description': '温度',
        'db_field': 'tem'
    },
    'U': {
        'var_name': 'U',
        'data_type': 'float',
        'scale_factor': 1.0,
        'description': '气象风场U分量',
        'db_field': 'u'
    },
    'V': {
        'var_name': 'V',
        'data_type': 'float',
        'scale_factor': 1.0,
        'description': '气象风场V分量',
        'db_field': 'v'
    },
    'VIS': {
        'var_name': 'VIS',
        'data_type': 'int',
        'scale_factor': 1.0,
        'description': '能见度',
        'db_field': 'vis'
    },
    'WEATHER': {
        'var_name': 'WEATHER',
        'data_type': 'int',
        'scale_factor': 1.0,
        'description': '天气现象编码',
        'db_field': 'weather'
    },
    'WIND': {
        'var_name': 'WIND',
        'data_type': 'int',
        'scale_factor': 1.0,
        'description': '风向编码',
        'db_field': 'wind'
    },
    'WINS': {
        'var_name': 'WINS',
        'data_type': 'int',
        'scale_factor': 1.0,
        'description': '风力编码',
        'db_field': 'wins'
    }
}

# ========== 标准格网编码/解码函数 ==========
# 0.01° 等纬经标准格网的 ID 方案

def encode_cell(lon: float, lat: float) -> int:
    """经纬 → cell_id（32bit 整数）"""
    ix = int(math.floor((lon + LON_OFF) / STEP))
    iy = int(math.floor((lat + LAT_OFF) / STEP))
    return (iy << 16) | ix          # 行主序打包

def decode_cell(cell_id: int) -> tuple[float, float]:
    """cell_id → 左下角 (lon_min, lat_min)"""
    ix = cell_id & 0xFFFF
    iy = cell_id >> 16
    lon_min = ix * STEP - LON_OFF
    lat_min = iy * STEP - LAT_OFF
    return lon_min, lat_min

def get_netcdf_indices_from_coords(lon: float, lat: float,
                                   nc_lon_min: float, nc_lat_min: float) -> tuple[int, int]:
    """根据经纬度坐标直接计算NetCDF索引"""
    lon_idx = int(round((lon - nc_lon_min) / STEP))
    lat_idx = int(round((lat - nc_lat_min) / STEP))
    return lat_idx, lon_idx

def get_obs_nc_files_path():
    """
    获取要处理的实况NC文件路径
    如果启用下载功能，先尝试下载最新数据
    
    Returns:
        tuple: (nc_files_list, downloaded_files_info)
    """
    nc_files = []
    downloaded_files_info = {}
    
    # 检查是否启用下载功能
    if WEATHER_DOWNLOAD_CONFIG.get("enable_download", False) and WEATHER_DOWNLOAD_AVAILABLE:
        logger.info("启用下载功能，正在获取实况天气数据")
        
        try:
            # 下载实况数据
            download_type = 'gz_obsnc'
            logger.info(f"正在下载实况数据类型 [{download_type}]")
            
            latest_files = download_and_get_latest_weather_data(download_type)
            if latest_files:
                # 获取解压后的所有NC文件
                nc_files = find_obs_nc_files_in_directories()
                if nc_files:
                    downloaded_files_info = {
                        'downloaded': True,
                        'download_type': download_type,
                        'files': nc_files
                    }
                    logger.info(f"成功下载并获取实况数据: {len(nc_files)} 个文件")
                else:
                    logger.warning("下载实况数据后未找到NC文件")
            else:
                logger.warning("下载实况数据失败，尝试使用本地文件")
                # 下载失败，尝试使用本地文件
                nc_files = find_obs_nc_files_in_directories()
                if nc_files:
                    downloaded_files_info = {
                        'downloaded': False,
                        'download_type': download_type,
                        'files': nc_files
                    }
                    logger.info(f"使用本地实况文件: {len(nc_files)} 个")
        except Exception as e:
            logger.error(f"处理实况数据时发生错误: {e}")
            # 发生错误，尝试使用本地文件
            nc_files = find_obs_nc_files_in_directories()
            if nc_files:
                downloaded_files_info = {
                    'downloaded': False,
                    'download_type': 'gz_obsnc',
                    'files': nc_files
                }
                logger.info(f"使用本地实况文件: {len(nc_files)} 个")
    else:
        # 未启用下载功能，直接使用本地文件
        logger.info("未启用下载功能，使用本地实况NC文件")
        nc_files = find_obs_nc_files_in_directories()
        if nc_files:
            downloaded_files_info = {
                'downloaded': False,
                'download_type': 'gz_obsnc',
                'files': nc_files
            }
            logger.info(f"使用本地实况文件: {len(nc_files)} 个")
        else:
            logger.warning("未找到本地实况NC文件")
    
    return nc_files, downloaded_files_info

def find_obs_nc_files_in_directories() -> list:
    """查找实况数据目录下的NC文件"""
    from config import SCRIPT_DIR
    
    # 实况数据存储在data/test目录（测试阶段）和data/obs目录（正式阶段）
    search_dirs = [
        str(SCRIPT_DIR / 'data/test'),
        str(SCRIPT_DIR / 'data/obs'),
        str(SCRIPT_DIR / 'data/obs/nc_files')
    ]
    
    nc_files = []
    for base_dir in search_dirs:
        if os.path.exists(base_dir):
            for root, _, files in os.walk(base_dir):
                for file in files:
                    if file.endswith('.nc'):
                        nc_files.append(os.path.join(root, file))
    
    logger.info(f"找到实况NC文件 {len(nc_files)} 个")
    return sorted(nc_files)


class AsyncObsDatabaseManager:
    """异步实况数据库管理器"""

    def __init__(self, pg_url: str, max_connections: int = 50):
        self.pg_url = pg_url
        self.max_connections = max_connections
        self.pool = None

    async def initialize(self):
        """初始化连接池"""
        # 解析PostgreSQL URL
        import urllib.parse as urlparse
        parsed = urlparse.urlparse(self.pg_url)

        # 解码密码中的特殊字符
        password = urlparse.unquote_plus(parsed.password) if parsed.password else None

        logger.info(f"连接数据库: {parsed.hostname}:{parsed.port or 5432}/{parsed.path[1:]} 用户: {parsed.username}")

        self.pool = await asyncpg.create_pool(
            host=parsed.hostname,
            port=parsed.port or 5432,
            user=parsed.username,
            password=password,
            database=parsed.path[1:],  # 去掉开头的 '/'
            min_size=5,
            max_size=self.max_connections,
            command_timeout=300
        )
        logger.info(f"数据库连接池初始化完成，最大连接数: {self.max_connections}")

    async def close(self):
        """关闭连接池"""
        if self.pool:
            await self.pool.close()
            logger.info("数据库连接池已关闭")

    async def upsert_obs_data(self, timestamp, time_data: Dict):
        """异步upsert实况数据"""
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                # 1. 确定当前处理的数据字段
                data_fields_in_batch = set()
                for cell_id, obs_data in time_data.items():
                    for field_name in OBS_FIELD_TYPES.keys():
                        if obs_data.get(field_name) is not None:
                            data_fields_in_batch.add(field_name)

                # 2. 先将该时间点的相关字段设置为null
                if data_fields_in_batch:
                    update_clauses = []
                    for field_name in data_fields_in_batch:
                        db_field = OBS_FIELD_TYPES[field_name]['db_field']
                        update_clauses.append(f"{db_field} = NULL")

                    if update_clauses:
                        update_sql = f"""
                            UPDATE weather_cell_obs
                            SET {', '.join(update_clauses)}
                            WHERE pre_time = $1
                        """
                        await conn.execute(update_sql, timestamp)
                        logger.info(f"已将时间 {timestamp} 的字段 {list(data_fields_in_batch)} 设置为NULL")

                # 3. 创建临时表
                tmp_tbl = f"tmp_weather_obs_{uuid.uuid4().hex}"
                await conn.execute(f"""
                    CREATE TEMP TABLE {tmp_tbl} (
                        pre_time TIMESTAMP NOT NULL,
                        cell_id INTEGER NOT NULL,
                        pre10m NUMERIC(8,3),
                        pre1h NUMERIC(8,3),
                        prs NUMERIC(8,2),
                        prs_sea NUMERIC(8,2),
                        rhu NUMERIC(5,2),
                        tem NUMERIC(8,3),
                        u NUMERIC(8,3),
                        v NUMERIC(8,3),
                        vis INTEGER,
                        weather INTEGER,
                        wind INTEGER,
                        wins INTEGER
                    ) ON COMMIT DROP;
                """)

                # 4. 准备数据
                data_rows = []
                for cell_id, obs_data in time_data.items():
                    row_data = [timestamp, cell_id]

                    # 按照表结构顺序添加字段值
                    field_order = ['PRE10m', 'PRE1h', 'PRS', 'PRSSea', 'RHU', 'TEM', 'U', 'V', 'VIS', 'WEATHER', 'WIND', 'WINS']
                    for field_name in field_order:
                        val = obs_data.get(field_name)
                        if val is not None:
                            field_config = OBS_FIELD_TYPES[field_name]
                            if field_config['data_type'] == 'float':
                                row_data.append(float(val) * field_config['scale_factor'])
                            else:
                                row_data.append(int(val) * field_config['scale_factor'])
                        else:
                            row_data.append(None)

                    data_rows.append(tuple(row_data))

                # 5. 批量插入临时表
                await conn.copy_records_to_table(
                    tmp_tbl,
                    records=data_rows,
                    columns=['pre_time', 'cell_id', 'pre10m', 'pre1h', 'prs', 'prs_sea', 'rhu', 'tem', 'u', 'v', 'vis', 'weather', 'wind', 'wins']
                )

                # 6. Upsert到目标表
                result = await conn.execute(f"""
                    INSERT INTO weather_cell_obs (pre_time, cell_id, pre10m, pre1h, prs, prs_sea, rhu, tem, u, v, vis, weather, wind, wins)
                    SELECT pre_time, cell_id, pre10m, pre1h, prs, prs_sea, rhu, tem, u, v, vis, weather, wind, wins
                    FROM {tmp_tbl}
                    ON CONFLICT (pre_time, cell_id) DO UPDATE SET
                        pre10m = CASE WHEN EXCLUDED.pre10m IS NOT NULL THEN EXCLUDED.pre10m ELSE weather_cell_obs.pre10m END,
                        pre1h = CASE WHEN EXCLUDED.pre1h IS NOT NULL THEN EXCLUDED.pre1h ELSE weather_cell_obs.pre1h END,
                        prs = CASE WHEN EXCLUDED.prs IS NOT NULL THEN EXCLUDED.prs ELSE weather_cell_obs.prs END,
                        prs_sea = CASE WHEN EXCLUDED.prs_sea IS NOT NULL THEN EXCLUDED.prs_sea ELSE weather_cell_obs.prs_sea END,
                        rhu = CASE WHEN EXCLUDED.rhu IS NOT NULL THEN EXCLUDED.rhu ELSE weather_cell_obs.rhu END,
                        tem = CASE WHEN EXCLUDED.tem IS NOT NULL THEN EXCLUDED.tem ELSE weather_cell_obs.tem END,
                        u = CASE WHEN EXCLUDED.u IS NOT NULL THEN EXCLUDED.u ELSE weather_cell_obs.u END,
                        v = CASE WHEN EXCLUDED.v IS NOT NULL THEN EXCLUDED.v ELSE weather_cell_obs.v END,
                        vis = CASE WHEN EXCLUDED.vis IS NOT NULL THEN EXCLUDED.vis ELSE weather_cell_obs.vis END,
                        weather = CASE WHEN EXCLUDED.weather IS NOT NULL THEN EXCLUDED.weather ELSE weather_cell_obs.weather END,
                        wind = CASE WHEN EXCLUDED.wind IS NOT NULL THEN EXCLUDED.wind ELSE weather_cell_obs.wind END,
                        wins = CASE WHEN EXCLUDED.wins IS NOT NULL THEN EXCLUDED.wins ELSE weather_cell_obs.wins END
                """)

                return len(data_rows)

    async def cleanup_null_records(self):
        """清理空记录 - 删除所有字段都为NULL的记录"""
        async with self.pool.acquire() as conn:
            result = await conn.execute("""
                DELETE FROM weather_cell_obs
                WHERE pre10m IS NULL AND pre1h IS NULL AND prs IS NULL AND prs_sea IS NULL
                  AND rhu IS NULL AND tem IS NULL AND u IS NULL AND v IS NULL
                  AND vis IS NULL AND weather IS NULL AND wind IS NULL AND wins IS NULL
            """)
            deleted_count = int(result.split()[-1]) if result and result.split() else 0
            if deleted_count > 0:
                logger.info(f"清理了 {deleted_count} 条空记录")
            return result

    async def call_obs_stored_procedure(self, procedure_type: str, precipitation_type: str, timestamp):
        """异步调用实况存储过程，失败后不重试"""
        try:
            # 设置10分钟超时，避免无限等待
            async with asyncio.timeout(600):  # 10分钟超时
                async with self.pool.acquire() as conn:
                    if procedure_type == "polygon":
                        if precipitation_type == "10min":
                            await conn.execute("""
                                CALL sp_precip_polygon($1::timestamp, $2::timestamp, $3::text, $4::text[], $5::text, $6::text, $7::text, $8::text)
                            """, timestamp, None, 'get_rainfall_type_obs_10m', ['pre10m'], 'weather_cell_obs',
                                 'pre10m', 'obs_precipitation_10min_polygon', 'obs_precipitation_10min_relation')
                        elif precipitation_type == "1h":
                            await conn.execute("""
                                CALL sp_precip_polygon($1::timestamp, $2::timestamp, $3::text, $4::text[], $5::text, $6::text, $7::text, $8::text)
                            """, timestamp, None, 'get_rainfall_type_obs_1h', ['pre1h'], 'weather_cell_obs',
                                 'pre1h', 'obs_precipitation_1h_polygon', 'obs_precipitation_1h_relation')

                    elif procedure_type == "line":
                        if precipitation_type == "10min":
                            await conn.execute("""
                                CALL sp_precip_line($1::timestamp, $2::timestamp, $3::text, $4::text[], $5::text, $6::text, $7::text)
                            """, timestamp, None, 'get_rainfall_type_obs_10m', ['pre10m'], 'weather_cell_obs',
                                 'pre10m', 'obs_precipitation_10min_line')
                        elif precipitation_type == "1h":
                            await conn.execute("""
                                CALL sp_precip_line($1::timestamp, $2::timestamp, $3::text, $4::text[], $5::text, $6::text, $7::text)
                            """, timestamp, None, 'get_rainfall_type_obs_1h', ['pre1h'], 'weather_cell_obs',
                                 'pre1h', 'obs_precipitation_1h_line')

                    return True
        except asyncio.TimeoutError:
            logger.error(f"实况存储过程调用超时 {procedure_type} {precipitation_type} {timestamp}: 超过10分钟，放弃执行")
            return False
        except Exception as e:
            logger.error(f"实况存储过程调用失败 {procedure_type} {precipitation_type} {timestamp}: {e}")
            return False


def process_obs_nc_files_cpu_intensive(nc_files: list, t_index_slice, valid_cells) -> Dict:
    """
    CPU密集型任务：在独立进程中处理实况NetCDF文件
    返回处理后的数据，不直接操作数据库
    """
    all_time_data = {}

    logger.info(f"PID {os.getpid()} 开始处理实况数据")

    for nc_file in nc_files:
        try:
            ds = xr.open_dataset(nc_file, engine="netcdf4")

            # 调试：打印NC文件的详细信息
            logger.info(f"=== 调试NC文件信息: {os.path.basename(nc_file)} ===")
            logger.info(f"文件维度: {dict(ds.dims)}")
            logger.info(f"坐标变量: {list(ds.coords.keys())}")
            logger.info(f"数据变量: {list(ds.data_vars.keys())}")

            # 打印所有时间相关的坐标和变量
            for coord_name in ds.coords:
                if 'time' in coord_name.lower() or 'ref' in coord_name.lower():
                    coord_data = ds.coords[coord_name]
                    logger.info(f"时间坐标 {coord_name}: shape={coord_data.shape}, dtype={coord_data.dtype}")
                    logger.info(f"  值: {coord_data.values}")
                    if hasattr(coord_data, 'attrs'):
                        logger.info(f"  属性: {coord_data.attrs}")

            # 检查是否有time变量作为数据变量
            for var_name in ds.data_vars:
                if 'time' in var_name.lower():
                    var_data = ds.data_vars[var_name]
                    logger.info(f"时间数据变量 {var_name}: shape={var_data.shape}, dtype={var_data.dtype}")
                    logger.info(f"  值: {var_data.values}")
                    if hasattr(var_data, 'attrs'):
                        logger.info(f"  属性: {var_data.attrs}")

            # 获取NetCDF坐标范围
            lon_vals = ds.coords['lon'].values
            lat_vals = ds.coords['lat'].values
            nc_lon_min, nc_lat_min = lon_vals[0], lat_vals[0]

            # 预计算NetCDF索引
            nc_lat_indices = []
            nc_lon_indices = []
            for cell_id in valid_cells:
                lon_min, lat_min = decode_cell(cell_id)
                lat_idx, lon_idx = get_netcdf_indices_from_coords(
                    lon_min, lat_min, nc_lon_min, nc_lat_min)
                nc_lat_indices.append(lat_idx)
                nc_lon_indices.append(lon_idx)

            nc_lat_indices = np.array(nc_lat_indices)
            nc_lon_indices = np.array(nc_lon_indices)

            # 处理时间切片 - 实况数据的时间维度处理
            # 尝试多种时间解析方式
            ts = None

            # 方法1: 检查是否有time数据变量（实况数据的实际时间）
            if 'time' in ds.data_vars:
                time_var = ds.data_vars['time']
                logger.info(f"找到time数据变量: {time_var.values}")
                # 实况数据的time变量通常是相对于reftime的小时偏移
                if 'reftime' in ds.coords:
                    reftime_str = str(ds.coords['reftime'].values[0])
                    reftime = pd.to_datetime(reftime_str, format='%Y%m%d%H%M')
                    # time变量的值是相对于reftime的小时数
                    time_offset_hours = float(time_var.values[0])
                    ts = reftime + pd.Timedelta(hours=time_offset_hours)
                    logger.info(f"解析时间: reftime={reftime}, offset={time_offset_hours}h, 实际时间={ts}")
                else:
                    # 直接解析time变量
                    ts = pd.to_datetime(time_var.values[0])
                    logger.info(f"直接解析time变量: {ts}")

            # 方法2: 如果没有time数据变量，使用reftime
            elif 'reftime' in ds.coords:
                base_time_str = str(ds.coords['reftime'].values[0])
                ts = pd.to_datetime(base_time_str, format='%Y%m%d%H%M')
                logger.info(f"使用reftime: {ts}")

            # 方法3: 检查其他可能的时间坐标
            else:
                for coord_name in ds.coords:
                    if 'time' in coord_name.lower():
                        try:
                            ts = pd.to_datetime(ds.coords[coord_name].values[0])
                            logger.info(f"使用坐标 {coord_name}: {ts}")
                            break
                        except:
                            continue

            if ts is None:
                logger.warning(f"无法解析文件 {nc_file} 的时间信息，跳过")
                continue

            logger.info(f"最终使用时间戳: {ts}")
            logger.info("=" * 50)

                # 检查实际的时间维度名称
                time_dim = None
                if 'time0' in ds.dims:
                    time_dim = 'time0'
                elif 'time' in ds.dims:
                    time_dim = 'time'
                else:
                    logger.warning(f"未找到时间维度，跳过文件 {nc_file}")
                    continue

                # 获取时间维度大小
                time_size = ds.dims[time_dim]

                for ti in range(min(t_index_slice.start, time_size),
                               min(t_index_slice.stop, time_size)):

                    if ts not in all_time_data:
                        all_time_data[ts] = {}

                    # 处理所有实况字段
                    for field_name, field_config in OBS_FIELD_TYPES.items():
                        var_name = field_config['var_name']

                        if var_name not in ds.data_vars:
                            continue

                        data_var = ds[var_name]
                        # 使用正确的时间维度名称进行索引
                        if time_dim == 'time0':
                            data_2d = data_var.isel(time0=ti).values
                        else:
                            data_2d = data_var.isel(time=ti).values
                        vals = data_2d[nc_lat_indices, nc_lon_indices]

                        # 数据验证
                        if field_config['data_type'] == 'float':
                            msk = ~np.isnan(vals) & np.isfinite(vals)
                        else:
                            msk = (vals >= 0) & (vals < 32767)

                        # 存储有效数据
                        valid_indices = np.where(msk)[0]
                        for i in valid_indices:
                            cell_id = valid_cells[i]
                            val = vals[i]

                            if field_config['data_type'] == 'float':
                                converted_val = float(val) * field_config['scale_factor']
                            else:
                                converted_val = int(val) * field_config['scale_factor']

                            # 对于降水数据，只存储降水量大于0的记录
                            if field_name in ['PRE10m', 'PRE1h'] and converted_val <= 0:
                                continue

                            if cell_id not in all_time_data[ts]:
                                all_time_data[ts][cell_id] = {}

                            all_time_data[ts][cell_id][field_name] = converted_val
            else:
                logger.warning(f"文件 {nc_file} 没有reftime坐标，跳过处理")

            ds.close()

        except Exception as e:
            logger.error(f"处理文件 {nc_file} 时出错: {e}")
            continue

    return all_time_data


async def process_obs_time_data_async(db_manager: AsyncObsDatabaseManager, all_time_data: Dict):
    """
    异步处理实况时间数据：并发执行数据库upsert操作
    """
    logger.info(f"开始异步处理 {len(all_time_data)} 个时间步的实况数据")

    # 创建信号量限制并发数
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_DB_OPERATIONS)

    async def process_single_timestamp(ts, time_data):
        async with semaphore:
            try:
                if time_data:
                    upserted_count = await db_manager.upsert_obs_data(ts, time_data)
                    logger.info(f"时间步 {ts}: upsert {upserted_count} 个格网")
                    return ts, True
                else:
                    logger.info(f"时间步 {ts}: 无新数据")
                    return ts, False
            except Exception as e:
                logger.error(f"处理时间步 {ts} 时出错: {e}")
                return ts, False

    # 并发处理所有时间步
    tasks = [process_single_timestamp(ts, time_data)
             for ts, time_data in all_time_data.items()]

    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 统计结果
    successful_timestamps = []
    for result in results:
        if isinstance(result, tuple) and result[1]:
            successful_timestamps.append(result[0])

    logger.info(f"异步实况数据处理完成，成功: {len(successful_timestamps)}/{len(all_time_data)}")
    return successful_timestamps


async def call_obs_stored_procedures_async(db_manager: AsyncObsDatabaseManager, processed_timestamps: List):
    """
    异步调用实况存储过程：并发执行polygon和line存储过程
    """
    if not processed_timestamps:
        logger.info("无处理时间戳，跳过实况存储过程")
        return

    logger.info(f"开始异步调用实况存储过程，处理 {len(processed_timestamps)} 个时间步")

    # 首先检查哪些时间步有降水数据
    valid_timestamps_10min = []
    valid_timestamps_1h = []

    async with db_manager.pool.acquire() as conn:
        for ts in processed_timestamps:
            try:
                # 检查10分钟降水数据
                result_10min = await conn.fetchval("""
                    SELECT COUNT(*) FROM weather_cell_obs
                    WHERE pre_time = $1 AND pre10m IS NOT NULL AND pre10m > 0
                """, ts)

                if result_10min > 0:
                    valid_timestamps_10min.append(ts)
                else:
                    logger.info(f"时间步 {ts}: 无10分钟降水数据，跳过相关存储过程")

                # 检查1小时降水数据
                result_1h = await conn.fetchval("""
                    SELECT COUNT(*) FROM weather_cell_obs
                    WHERE pre_time = $1 AND pre1h IS NOT NULL AND pre1h > 0
                """, ts)

                if result_1h > 0:
                    valid_timestamps_1h.append(ts)
                else:
                    logger.info(f"时间步 {ts}: 无1小时降水数据，跳过相关存储过程")

            except Exception as e:
                logger.error(f"检查时间步 {ts} 数据失败: {e}")
                continue

    if not valid_timestamps_10min and not valid_timestamps_1h:
        logger.info("无有效降水数据，跳过实况存储过程")
        return

    # 创建任务列表：每个时间步 × 2种类型(line/polygon) × 2种降水类型(10min/1h)
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_PROCEDURES)

    async def execute_procedure_task(ts, procedure_type, precipitation_type):
        async with semaphore:
            try:
                success = await db_manager.call_obs_stored_procedure(procedure_type, precipitation_type, ts)
                if success:
                    logger.info(f"✓ 完成实况存储过程 {procedure_type} {precipitation_type} for {ts}")
                else:
                    logger.warning(f"✗ 实况存储过程失败 {procedure_type} {precipitation_type} for {ts}，不再重试")
                return success
            except Exception as e:
                logger.error(f"✗ 实况存储过程异常 {procedure_type} {precipitation_type} for {ts}: {e}，不再重试")
                return False

    # 创建所有任务
    tasks = []
    for ts in valid_timestamps_10min:
        tasks.append(execute_procedure_task(ts, "polygon", "10min"))
        tasks.append(execute_procedure_task(ts, "line", "10min"))

    for ts in valid_timestamps_1h:
        tasks.append(execute_procedure_task(ts, "polygon", "1h"))
        tasks.append(execute_procedure_task(ts, "line", "1h"))

    logger.info(f"并发执行 {len(tasks)} 个实况存储过程任务...")

    # 并发执行所有存储过程，失败后不重试
    try:
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 统计结果
        success_count = sum(1 for r in results if r is True)
        failed_count = len(tasks) - success_count

        logger.info(f"实况存储过程调用完成，成功: {success_count}/{len(tasks)}")
        if failed_count > 0:
            logger.warning(f"有 {failed_count} 个实况存储过程失败，已跳过不再重试")

    except Exception as e:
        logger.error(f"实况存储过程批量执行异常: {e}，停止所有存储过程调用")


async def hybrid_obs_worker_async(nc_files: list, t_index_slice, valid_cells):
    """
    混合架构的异步工作函数：
    1. 使用进程池处理CPU密集型的NetCDF解析
    2. 使用协程处理I/O密集型的数据库操作
    """
    # 初始化数据库管理器
    db_manager = AsyncObsDatabaseManager(PG_URL)
    await db_manager.initialize()

    try:
        logger.info(f"开始混合处理实况数据 time index {t_index_slice.start}-{t_index_slice.stop-1}")

        # 1. 使用进程池处理CPU密集型任务
        with ProcessPoolExecutor(max_workers=1) as executor:
            loop = asyncio.get_event_loop()
            all_time_data = await loop.run_in_executor(
                executor,
                process_obs_nc_files_cpu_intensive,
                nc_files, t_index_slice, valid_cells
            )

        # 2. 使用协程处理I/O密集型任务
        if all_time_data:
            processed_timestamps = await process_obs_time_data_async(db_manager, all_time_data)

            # 3. 清理空记录
            await db_manager.cleanup_null_records()

            # 4. 异步调用存储过程
            await call_obs_stored_procedures_async(db_manager, processed_timestamps)

        logger.info(f"完成混合处理实况数据 time index {t_index_slice.start}-{t_index_slice.stop-1}")

    finally:
        await db_manager.close()


async def process_obs_weather_data_hybrid(nc_files=None):
    """
    混合架构的实况天气数据处理（供调度器调用）

    Args:
        nc_files: 已下载的NC文件列表，None表示自动获取
    """
    logger.info("=== 混合架构实况天气数据处理系统启动 ===")

    try:
        # 1. 获取NC文件路径
        if nc_files:
            # 使用调度器提供的已下载文件
            logger.info(f"使用调度器提供的实况NC文件: {len(nc_files)} 个")
            downloaded_files_info = {}
        else:
            # 自动获取NC文件路径（可能包含下载）
            nc_files, downloaded_files_info = get_obs_nc_files_path()

        if not nc_files:
            logger.error("未找到任何实况NC文件，程序退出")
            return

        logger.info(f"将处理实况NC文件: {len(nc_files)} 个")
        source = "下载" if downloaded_files_info.get('downloaded', False) else "本地现有"
        logger.info(f"  文件来源: {source}")

        # 2. 预读LUT cell_id
        logger.info("预读 LUT cell_id …")
        engine = create_engine(PG_URL)
        with engine.begin() as conn:
            valid_cells = pd.read_sql("""
                SELECT DISTINCT cell_id
                FROM weather_cell_route_lut
            """, conn)['cell_id'].values
        valid_cells.sort()
        logger.info(f"有效格网 {len(valid_cells):,}")

        # 3. 确定时间步数量
        if not nc_files:
            logger.error("未找到参考NC文件")
            return

        with xr.open_dataset(nc_files[0], engine="netcdf4") as ds0:
            # 实况数据的时间维度可能是time0或time
            if 'time0' in ds0.dims:
                nt = ds0.sizes['time0']
            elif 'time' in ds0.dims:
                nt = ds0.sizes['time']
            else:
                logger.error("未找到时间维度")
                return
        logger.info(f"NetCDF 共 {nt} 个时间步")

        # 4. 创建时间切片
        CHUNK_SIZE = PROCESSING_CONFIG["chunk_size"]
        slices = []
        for start in range(0, nt, CHUNK_SIZE):
            stop = min(start + CHUNK_SIZE, nt)
            slices.append(slice(start, stop))

        # 5. 使用混合架构处理：每个切片使用一个协程
        logger.info(f"使用混合架构处理 {len(slices)} 个时间切片...")

        # 创建协程任务
        tasks = []
        for slice_obj in slices:
            task = hybrid_obs_worker_async(nc_files, slice_obj, valid_cells)
            tasks.append(task)

        # 并发执行所有任务（限制并发数避免资源耗尽）
        semaphore = asyncio.Semaphore(3)  # 最多3个并发切片处理

        async def limited_task(task):
            async with semaphore:
                await task

        limited_tasks = [limited_task(task) for task in tasks]
        await asyncio.gather(*limited_tasks)

        logger.info("✓ 全部实况数据切片处理完成")

        # 6. 处理完成后备份文件（如果是下载的文件且启用备份）
        if (WEATHER_DOWNLOAD_CONFIG.get("enable_backup", True) and
            WEATHER_DOWNLOAD_AVAILABLE and downloaded_files_info.get('downloaded', False)):
            logger.info("开始备份处理完的实况文件...")

            files_to_backup = downloaded_files_info.get('files', [])
            download_type = downloaded_files_info.get('download_type')

            backup_success_count = 0
            for file_path in files_to_backup:
                try:
                    backup_success = process_and_backup_weather_file(
                        file_path, True, download_type
                    )
                    if backup_success:
                        backup_success_count += 1
                    else:
                        logger.warning(f"备份文件失败: {file_path}")
                except Exception as e:
                    logger.error(f"备份文件 {file_path} 时发生错误: {e}")

            logger.info(f"成功备份 {backup_success_count}/{len(files_to_backup)} 个实况文件")
        else:
            logger.info("跳过文件备份（未启用备份功能或下载模块不可用）")

        logger.info("=== 混合架构实况天气数据处理完成 ===")

    except Exception as e:
        logger.error(f"实况数据处理过程中发生错误: {e}")
        raise


async def main():
    """主函数：混合架构的实况天气数据处理（命令行版本）"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='实况天气数据处理系统（异步版本）')
    parser.add_argument('--test', action='store_true', help='使用测试数据（data/test目录）')

    args = parser.parse_args()

    if args.test:
        logger.info("使用测试模式，处理data/test目录下的实况数据")

    # 调用处理函数
    await process_obs_weather_data_hybrid()


if __name__ == '__main__':
    asyncio.run(main())
